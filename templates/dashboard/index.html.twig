{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
            <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Domains
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_domains }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total URLs
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_urls }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-link fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Indexed URLs
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.indexed_urls }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending URLs
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.pending_urls }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/domains" class="btn btn-primary btn-block">
                            <i class="fas fa-plus me-2"></i>Add Domain
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/urls" class="btn btn-success btn-block">
                            <i class="fas fa-upload me-2"></i>Submit URLs
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/sitemaps" class="btn btn-info btn-block">
                            <i class="fas fa-sitemap me-2"></i>Submit Sitemap
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/reports" class="btn btn-warning btn-block">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent URL Submissions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent URL Submissions</h6>
                <a href="/dashboard/urls" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_urls %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>URL</th>
                                    <th>Domain</th>
                                    <th>Provider</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for url in recent_urls %}
                                    <tr>
                                        <td>
                                            <a href="{{ url.url }}" target="_blank" class="text-decoration-none">
                                                {{ url.url|length > 50 ? url.url|slice(0, 50) ~ '...' : url.url }}
                                            </a>
                                        </td>
                                        <td>{{ url.domain }}</td>
                                        <td>
                                            <span class="badge bg-{{ url.provider == 'google' ? 'danger' : 'info' }}">
                                                {{ url.provider|title }}
                                            </span>
                                        </td>
                                        <td>
                                            {% set status_class = {
                                                'pending': 'warning',
                                                'submitted': 'info',
                                                'indexed': 'success',
                                                'failed': 'danger',
                                                'rejected': 'secondary'
                                            } %}
                                            <span class="badge bg-{{ status_class[url.status] ?? 'secondary' }}">
                                                {{ url.status|title }}
                                            </span>
                                        </td>
                                        <td>{{ url.created_at|date('M d, Y H:i') }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-link fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No URL submissions yet</h5>
                        <p class="text-muted">Start by adding a domain and submitting your first URL.</p>
                        <a href="/dashboard/domains" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Domain
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.btn-block {
    width: 100%;
}
</style>
{% endblock %}
