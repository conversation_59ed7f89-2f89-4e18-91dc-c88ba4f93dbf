<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title ?? 'SEO Indexer Platform' }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: calc(100vh - 56px);
        }
        .flash-messages {
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 1050;
            max-width: 400px;
        }
    </style>
    
    {% block head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-search me-2"></i>{{ app_name }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if is_logged_in %}
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        {% if is_admin %}
                            <li class="nav-item">
                                <a class="nav-link" href="/admin">
                                    <i class="fas fa-cog me-1"></i>Admin
                                </a>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if is_logged_in %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/dashboard">Dashboard</a></li>
                                <li><a class="dropdown-item" href="/profile">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/logout">Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="/login">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/register">Register</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="flash-messages">
        {% for type, message in flash_messages %}
            <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    </div>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% if is_logged_in %}
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard/domains">
                                    <i class="fas fa-globe me-2"></i>Domains
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard/urls">
                                    <i class="fas fa-link me-2"></i>URLs
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard/sitemaps">
                                    <i class="fas fa-sitemap me-2"></i>Sitemaps
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard/api-keys">
                                    <i class="fas fa-key me-2"></i>API Keys
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard/reports">
                                    <i class="fas fa-chart-bar me-2"></i>Reports
                                </a>
                            </li>
                        </ul>
                        
                        {% if is_admin %}
                            <hr>
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>Administration</span>
                            </h6>
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link" href="/admin">
                                        <i class="fas fa-cog me-2"></i>Overview
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/admin/users">
                                        <i class="fas fa-users me-2"></i>Users
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/admin/domains">
                                        <i class="fas fa-globe me-2"></i>Domains
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/admin/logs">
                                        <i class="fas fa-file-alt me-2"></i>Logs
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="/admin/settings">
                                        <i class="fas fa-cogs me-2"></i>Settings
                                    </a>
                                </li>
                            </ul>
                        {% endif %}
                    </div>
                </nav>

                <!-- Main content area -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            {% else %}
                <main class="col-12 main-content">
            {% endif %}
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
