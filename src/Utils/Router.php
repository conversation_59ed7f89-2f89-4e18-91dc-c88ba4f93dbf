<?php

declare(strict_types=1);

namespace App\Utils;

use App\App;
use App\Controllers\HomeController;
use App\Controllers\AuthController;
use App\Controllers\DashboardController;
use App\Controllers\AdminController;
use App\Controllers\ApiController;

class Router
{
    private App $app;
    private array $routes = [];

    public function __construct(App $app)
    {
        $this->app = $app;
        $this->defineRoutes();
    }

    private function defineRoutes(): void
    {
        // Public routes
        $this->routes = [
            'GET' => [
                '/' => [HomeController::class, 'index'],
                '/login' => [AuthController::class, 'showLogin'],
                '/register' => [AuthController::class, 'showRegister'],
                '/auth/google' => [AuthController::class, 'googleAuth'],
                '/auth/google/callback' => [AuthController::class, 'googleCallback'],
                '/auth/bing' => [AuthController::class, 'bingAuth'],
                '/auth/bing/callback' => [AuthController::class, 'bingCallback'],
                '/logout' => [AuthController::class, 'logout'],
                '/verify-email' => [AuthController::class, 'verifyEmail'],
                '/forgot-password' => [AuthController::class, 'showForgotPassword'],
                '/reset-password' => [AuthController::class, 'showResetPassword'],
                
                // Dashboard routes (protected)
                '/dashboard' => [DashboardController::class, 'index'],
                '/dashboard/domains' => [DashboardController::class, 'domains'],
                '/dashboard/urls' => [DashboardController::class, 'urls'],
                '/dashboard/sitemaps' => [DashboardController::class, 'sitemaps'],
                '/dashboard/api-keys' => [DashboardController::class, 'apiKeys'],
                '/dashboard/reports' => [DashboardController::class, 'reports'],
                '/dashboard/connections' => [DashboardController::class, 'connections'],
                
                // Admin routes (protected)
                '/admin' => [AdminController::class, 'index'],
                '/admin/users' => [AdminController::class, 'users'],
                '/admin/domains' => [AdminController::class, 'domains'],
                '/admin/logs' => [AdminController::class, 'logs'],
                '/admin/settings' => [AdminController::class, 'settings'],
            ],
            'POST' => [
                '/login' => [AuthController::class, 'login'],
                '/register' => [AuthController::class, 'register'],
                '/forgot-password' => [AuthController::class, 'forgotPassword'],
                '/reset-password' => [AuthController::class, 'resetPassword'],
                '/auth/google/disconnect' => [AuthController::class, 'disconnectGoogle'],
                '/auth/bing/disconnect' => [AuthController::class, 'disconnectBing'],
                
                // Dashboard actions
                '/dashboard/domains/add' => [DashboardController::class, 'addDomain'],
                '/dashboard/domains/verify' => [DashboardController::class, 'verifyDomain'],
                '/dashboard/urls/submit' => [DashboardController::class, 'submitUrls'],
                '/dashboard/sitemaps/submit' => [DashboardController::class, 'submitSitemap'],
                '/dashboard/api-keys/save' => [DashboardController::class, 'saveApiKeys'],
                
                // Admin actions
                '/admin/users/block' => [AdminController::class, 'blockUser'],
                '/admin/users/unblock' => [AdminController::class, 'unblockUser'],
                '/admin/settings/save' => [AdminController::class, 'saveSettings'],
            ],
            'DELETE' => [
                '/dashboard/domains/{id}' => [DashboardController::class, 'deleteDomain'],
                '/admin/users/{id}' => [AdminController::class, 'deleteUser'],
            ],
            'PUT' => [
                '/dashboard/api-keys' => [DashboardController::class, 'updateApiKeys'],
            ]
        ];

        // API routes
        $this->routes['GET']['/api/urls/status'] = [ApiController::class, 'getUrlStatus'];
        $this->routes['POST']['/api/urls/check'] = [ApiController::class, 'checkUrls'];
        $this->routes['GET']['/api/quotas'] = [ApiController::class, 'getQuotas'];
    }

    public function dispatch(): void
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remove trailing slash except for root
        if ($uri !== '/' && substr($uri, -1) === '/') {
            $uri = rtrim($uri, '/');
        }

        // Check if route exists
        if (!isset($this->routes[$method][$uri])) {
            // Try to match dynamic routes
            $matchedRoute = $this->matchDynamicRoute($method, $uri);
            if (!$matchedRoute) {
                $this->handleNotFound();
                return;
            }
            [$controllerClass, $action, $params] = $matchedRoute;
        } else {
            [$controllerClass, $action] = $this->routes[$method][$uri];
            $params = [];
        }

        // Instantiate controller and call action
        $controller = new $controllerClass($this->app);
        
        // Check if method exists
        if (!method_exists($controller, $action)) {
            $this->handleNotFound();
            return;
        }

        // Call the controller action
        call_user_func_array([$controller, $action], $params);
    }

    private function matchDynamicRoute(string $method, string $uri): ?array
    {
        if (!isset($this->routes[$method])) {
            return null;
        }

        foreach ($this->routes[$method] as $pattern => $handler) {
            if (strpos($pattern, '{') === false) {
                continue;
            }

            $regex = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
            $regex = '#^' . $regex . '$#';

            if (preg_match($regex, $uri, $matches)) {
                array_shift($matches); // Remove full match
                return [$handler[0], $handler[1], $matches];
            }
        }

        return null;
    }

    private function handleNotFound(): void
    {
        http_response_code(404);
        echo $this->app->getTwig()->render('errors/404.html.twig');
    }
}
