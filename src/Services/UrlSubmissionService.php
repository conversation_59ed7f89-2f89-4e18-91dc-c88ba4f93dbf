<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\GoogleSearchConsoleService;
use App\Services\BingWebmasterService;
use App\Services\GoogleOAuthService;
use App\Services\BingOAuthService;
use App\Services\DatabaseService;
use App\Models\OAuthToken;
use App\Utils\Config;
use Monolog\Logger;

class UrlSubmissionService
{
    private GoogleSearchConsoleService $googleService;
    private BingWebmasterService $bingService;
    private DatabaseService $db;
    private Config $config;
    private Logger $logger;
    private OAuthToken $tokenModel;

    public function __construct(
        DatabaseService $db,
        Config $config,
        Logger $logger
    ) {
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
        $this->tokenModel = new OAuthToken($db);

        // Initialize OAuth services
        $googleOAuth = new GoogleOAuthService($db, new \App\Services\SessionService($config), $config, $logger);
        $bingOAuth = new BingOAuthService($db, new \App\Services\SessionService($config), $config, $logger);

        // Initialize API services
        $this->googleService = new GoogleSearchConsoleService($googleOAuth, $db, $config, $logger);
        $this->bingService = new BingWebmasterService($bingOAuth, $db, $config, $logger);
    }

    public function submitUrl(int $userId, int $domainId, string $url, array $providers = ['google', 'bing']): array
    {
        $results = [];
        $overallSuccess = false;

        // Get domain information
        $domain = $this->db->fetch('SELECT * FROM domains WHERE id = ? AND user_id = ?', [$domainId, $userId]);
        if (!$domain) {
            return [
                'success' => false,
                'error' => 'Domain not found or access denied'
            ];
        }

        $siteUrl = $domain['protocol'] . '://' . $domain['domain'];

        // Submit to Google if requested and connected
        if (in_array('google', $providers) && $this->tokenModel->hasValidToken($userId, 'google')) {
            try {
                $googleResult = $this->googleService->submitUrl($userId, $url);
                $results['google'] = $googleResult;
                
                if ($googleResult['success']) {
                    $overallSuccess = true;
                    $this->recordSubmission($userId, $domainId, $url, 'google', 'submitted', $googleResult);
                } else {
                    $this->recordSubmission($userId, $domainId, $url, 'google', 'failed', $googleResult);
                }
            } catch (\Exception $e) {
                $results['google'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                $this->recordSubmission($userId, $domainId, $url, 'google', 'failed', ['error' => $e->getMessage()]);
            }
        } elseif (in_array('google', $providers)) {
            $results['google'] = [
                'success' => false,
                'error' => 'Google account not connected'
            ];
        }

        // Submit to Bing if requested and connected
        if (in_array('bing', $providers) && $this->tokenModel->hasValidToken($userId, 'bing')) {
            try {
                $bingResult = $this->bingService->submitUrl($userId, $siteUrl, $url);
                $results['bing'] = $bingResult;
                
                if ($bingResult['success']) {
                    $overallSuccess = true;
                    $this->recordSubmission($userId, $domainId, $url, 'bing', 'submitted', $bingResult);
                } else {
                    $this->recordSubmission($userId, $domainId, $url, 'bing', 'failed', $bingResult);
                }
            } catch (\Exception $e) {
                $results['bing'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                $this->recordSubmission($userId, $domainId, $url, 'bing', 'failed', ['error' => $e->getMessage()]);
            }
        } elseif (in_array('bing', $providers)) {
            $results['bing'] = [
                'success' => false,
                'error' => 'Bing account not connected'
            ];
        }

        return [
            'success' => $overallSuccess,
            'results' => $results,
            'message' => $this->generateSubmissionMessage($results)
        ];
    }

    public function submitUrlBatch(int $userId, int $domainId, array $urls, array $providers = ['google', 'bing']): array
    {
        $results = [];
        $successCount = 0;
        $totalUrls = count($urls);

        // Get domain information
        $domain = $this->db->fetch('SELECT * FROM domains WHERE id = ? AND user_id = ?', [$domainId, $userId]);
        if (!$domain) {
            return [
                'success' => false,
                'error' => 'Domain not found or access denied'
            ];
        }

        $siteUrl = $domain['protocol'] . '://' . $domain['domain'];

        // Process each URL
        foreach ($urls as $url) {
            $urlResult = $this->submitUrl($userId, $domainId, $url, $providers);
            $results[$url] = $urlResult;
            
            if ($urlResult['success']) {
                $successCount++;
            }
        }

        return [
            'success' => $successCount > 0,
            'results' => $results,
            'summary' => [
                'total' => $totalUrls,
                'successful' => $successCount,
                'failed' => $totalUrls - $successCount
            ],
            'message' => "Batch submission completed: {$successCount}/{$totalUrls} URLs submitted successfully"
        ];
    }

    public function checkUrlStatus(int $userId, string $url): array
    {
        $results = [];

        // Check Google status if connected
        if ($this->tokenModel->hasValidToken($userId, 'google')) {
            try {
                $googleStatus = $this->googleService->getUrlStatus($userId, $url);
                $results['google'] = $googleStatus;
            } catch (\Exception $e) {
                $results['google'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        // Bing doesn't have a direct URL status check API like Google
        // We would need to check the submission history from our database
        $bingSubmission = $this->db->fetch(
            'SELECT * FROM url_submissions WHERE user_id = ? AND url = ? AND provider = "bing" ORDER BY created_at DESC LIMIT 1',
            [$userId, $url]
        );

        if ($bingSubmission) {
            $results['bing'] = [
                'success' => true,
                'status' => $bingSubmission['status'],
                'submitted_at' => $bingSubmission['submitted_at'],
                'last_checked' => $bingSubmission['last_checked']
            ];
        }

        return [
            'success' => !empty($results),
            'results' => $results
        ];
    }

    public function getSubmissionHistory(int $userId, int $page = 1, int $perPage = 20): array
    {
        $sql = 'SELECT us.*, d.domain, d.protocol 
                FROM url_submissions us 
                JOIN domains d ON us.domain_id = d.id 
                WHERE us.user_id = ? 
                ORDER BY us.created_at DESC';

        return $this->db->paginate($sql, [$userId], $page, $perPage);
    }

    public function getSubmissionStats(int $userId): array
    {
        $stats = [];

        // Overall stats
        $stats['total'] = $this->db->count('url_submissions', ['user_id' => $userId]);
        $stats['google'] = $this->db->count('url_submissions', ['user_id' => $userId, 'provider' => 'google']);
        $stats['bing'] = $this->db->count('url_submissions', ['user_id' => $userId, 'provider' => 'bing']);

        // Status breakdown
        $statusStats = $this->db->fetchAll(
            'SELECT status, COUNT(*) as count FROM url_submissions WHERE user_id = ? GROUP BY status',
            [$userId]
        );

        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = (int)$stat['count'];
        }

        // Recent submissions (last 30 days)
        $stats['recent'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM url_submissions 
             WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
            [$userId]
        )['count'] ?? 0;

        return $stats;
    }

    private function recordSubmission(int $userId, int $domainId, string $url, string $provider, string $status, array $responseData): void
    {
        $data = [
            'user_id' => $userId,
            'domain_id' => $domainId,
            'url' => $url,
            'provider' => $provider,
            'submission_type' => 'url',
            'status' => $status,
            'response_data' => json_encode($responseData),
            'submitted_at' => date('Y-m-d H:i:s')
        ];

        if ($status === 'failed' && isset($responseData['error'])) {
            $data['error_message'] = $responseData['error'];
        }

        // Update provider-specific status
        if ($provider === 'google') {
            $data['google_status'] = $status;
        } elseif ($provider === 'bing') {
            $data['bing_status'] = $status;
        }

        $this->db->insert('url_submissions', $data);
    }

    private function generateSubmissionMessage(array $results): string
    {
        $messages = [];

        if (isset($results['google'])) {
            if ($results['google']['success']) {
                $messages[] = 'Google: Success';
            } else {
                $messages[] = 'Google: Failed - ' . $results['google']['error'];
            }
        }

        if (isset($results['bing'])) {
            if ($results['bing']['success']) {
                $messages[] = 'Bing: Success';
            } else {
                $messages[] = 'Bing: Failed - ' . $results['bing']['error'];
            }
        }

        return implode('; ', $messages);
    }

    public function getConnectedProviders(int $userId): array
    {
        return $this->tokenModel->getConnectedProviders($userId);
    }

    public function validateUrl(string $url): array
    {
        $errors = [];

        if (empty($url)) {
            $errors[] = 'URL is required';
        } elseif (!filter_var($url, FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid URL format';
        } elseif (!preg_match('/^https?:\/\//', $url)) {
            $errors[] = 'URL must start with http:// or https://';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    public function validateUrlBatch(array $urls): array
    {
        $results = [];
        $validUrls = [];
        $invalidUrls = [];

        foreach ($urls as $url) {
            $validation = $this->validateUrl($url);
            $results[$url] = $validation;

            if ($validation['valid']) {
                $validUrls[] = $url;
            } else {
                $invalidUrls[] = $url;
            }
        }

        return [
            'valid_urls' => $validUrls,
            'invalid_urls' => $invalidUrls,
            'validation_results' => $results,
            'summary' => [
                'total' => count($urls),
                'valid' => count($validUrls),
                'invalid' => count($invalidUrls)
            ]
        ];
    }
}
