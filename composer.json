{"name": "seo-indexer/platform", "description": "SEO Indexer Platform - Submit URLs to Google and Bing for indexing", "type": "project", "license": "MIT", "authors": [{"name": "SEO Indexer Team", "email": "<EMAIL>"}], "require": {"php": ">=8.0", "ext-pdo": "*", "ext-json": "*", "ext-curl": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^7.0", "league/oauth2-client": "^2.6", "league/oauth2-google": "^4.0", "thenetworg/oauth2-azure": "^2.0", "firebase/php-jwt": "^6.0", "vlucas/phpdotenv": "^5.4", "monolog/monolog": "^3.0", "respect/validation": "^2.2", "twig/twig": "^3.0"}, "require-dev": {"phpunit/phpunit": "^10.0", "phpstan/phpstan": "^1.8", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"App\\": "src/", "App\\Controllers\\": "src/Controllers/", "App\\Models\\": "src/Models/", "App\\Services\\": "src/Services/", "App\\Middleware\\": "src/Middleware/", "App\\Utils\\": "src/Utils/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "phpstan": "phpstan analyse src", "phpcs": "phpcs src --standard=PSR12", "phpcbf": "phpcbf src --standard=PSR12"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}